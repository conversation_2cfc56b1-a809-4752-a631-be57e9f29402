import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, On<PERSON><PERSON>roy } from '@angular/core';
import { GeometryToolBar, GeometryToolType } from '@viclass/editor.geo';
import { TOOLBAR, TOOLTYPE } from '../injection.token';
import { ToolListener, ToolListenerHost } from '../tool.listener';

/**
 * Component for displaying current selection message for IntersectionPointTool
 * Shows step-by-step instructions based on current selection state
 */
@Component({
    selector: 'tb-intersection-message',
    templateUrl: './intersection-message.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IntersectionMessageComponent implements AfterViewInit, OnDestroy, ToolListenerHost {
    private toolListener: ToolListener;
    private _currentMessage: string = '';
    private pollingInterval: any;
    private lastLoggedState: string = '';

    constructor(
        private changeDetectorRef: ChangeDetectorRef,
        @Inject(TOOLBAR) private toolbar: GeometryToolBar,
        @Inject(TOOLTYPE) private tooltype: GeometryToolType
    ) {
        this.toolListener = new ToolListener(this, tooltype, changeDetectorRef);
        // Initialize with initial message
        this._currentMessage = this.getInitialMessage();
    }

    ngAfterViewInit(): void {
        this.toolbar.registerToolListener(this.toolListener);
        // Use setTimeout to avoid ExpressionChangedAfterItHasBeenCheckedError
        setTimeout(() => {
            this.updateInputFromToolState();
        }, 0);

        // Start polling to check for tool state changes
        // Since intersection tool doesn't have specific tool state events,
        // we need to poll for changes in their internal state
        this.pollingInterval = setInterval(() => {
            this.updateInputFromToolState();
        }, 200); // Check every 200ms
    }

    ngOnDestroy(): void {
        this.toolbar.unregisterToolListener(this.toolListener);
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
        }
    }

    get displayMessage(): string {
        return this._currentMessage;
    }

    get currentMessage(): string {
        const tool = this.toolbar.getTool(this.tooltype);

        if (!tool) {
            return this.getInitialMessage();
        }

        // Access the tool's internal state to determine current step
        const intersectionTool = tool as any;

        // Check selLogic state to determine current step
        const selLogic = intersectionTool.selLogic;
        let currentStep = 1; // Default to step 1

        if (selLogic && selLogic.selected) {
            const selectedCount = selLogic.selected.length;
            
            if (selectedCount === 0) {
                currentStep = 1; // Select first element
            } else if (selectedCount === 1) {
                currentStep = 2; // Select second element
            } else if (selectedCount >= 2) {
                // Check if we have intersections available
                const hasIntersections = intersectionTool.intersections && intersectionTool.intersections.length > 0;
                if (hasIntersections) {
                    currentStep = 3; // Select intersection point
                } else {
                    // No intersections found, back to step 1
                    currentStep = 1;
                }
            }
        }

        // Create state string for comparison to avoid unnecessary updates
        const currentState = `${currentStep}`;
        this.lastLoggedState = currentState;

        if (currentStep === 1) {
            // Step 1: Select first element
            return this.getSelectFirstElementMessage();
        } else if (currentStep === 2) {
            // Step 2: Select second element
            return this.getSelectSecondElementMessage();
        } else if (currentStep >= 3) {
            // Step 3: Select intersection point
            return this.getSelectIntersectionMessage();
        }

        return this.getInitialMessage();
    }

    private getInitialMessage(): string {
        return 'Bắt đầu tạo giao điểm';
    }

    private getSelectFirstElementMessage(): string {
        return 'Chọn đường thẳng, đường tròn hoặc hình đầu tiên';
    }

    private getSelectSecondElementMessage(): string {
        return 'Chọn đường thẳng, đường tròn hoặc hình thứ hai để tìm giao điểm';
    }

    private getSelectIntersectionMessage(): string {
        return 'Chọn giao điểm mong muốn từ các giao điểm có sẵn';
    }

    updateInputFromToolState() {
        // Use setTimeout to avoid ExpressionChangedAfterItHasBeenCheckedError
        setTimeout(() => {
            const newMessage = this.currentMessage;
            if (this._currentMessage !== newMessage) {
                this._currentMessage = newMessage;
                this.changeDetectorRef.detectChanges();
            }
        }, 0);
    }
}
