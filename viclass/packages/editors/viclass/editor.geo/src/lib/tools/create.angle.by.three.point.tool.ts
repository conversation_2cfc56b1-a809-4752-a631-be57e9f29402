import { point, vector } from '@flatten-js/core';
import { ErrorHandlerDecorator } from '@viclass/editor.core';
import { geoDefaultHandlerFn, GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { CommonToolState, RenderAngle, RenderLine, RenderLineSegment, RenderVertex } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { angleCCW, pAngle, pLine, PreviewQueue } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import { nPoints, SelectableType, then, ThenSelector, vert, vertex } from '../selectors';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import { assignNames, getFocusDocCtrl, handleIfPointerNotInError, remoteConstruct } from './util.tool';
import { buildAngleConstructionFromLines } from './util.construction';
import { getLargerIdx } from '../nth.direction';

/**
 * Refactored: CreateAngleByThreePointsTool
 * Creates angles from three points with proper counter-clockwise arc calculation.
 */
export class CreateAngleByThreePointsTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateAngleByThreePointsTool';

    declare selLogic: ThenSelector;
    pQ = new PreviewQueue();
    private previewAngle?: RenderAngle;
    private previewLine1?: RenderLine;
    private previewLine2?: RenderLine;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    override resetState() {
        if (this.selLogic) this.selLogic.reset();
        super.resetState();
    }

    private createSelLogic() {
        this.selLogic = then(
            [
                nPoints(this.pQ, this.pointerHandler.cursor, {
                    count: 3,
                }),
                vertex({
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                    syncPreview: false,
                }),
            ],
            {
                onComplete: (selector: ThenSelector, doc: GeoDocCtrl) =>
                    this.performConstructionFromPreview(selector.selected, doc),
            }
        );
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl));

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private calculatePreviewAngle(
        p1: RenderVertex,
        root: RenderVertex,
        p2: RenderVertex,
        dir: RenderVertex
    ): {
        startLine: RenderLine;
        endLine: RenderLine;
        startLineVDir: 1 | -1;
        endLineVDir: 1 | -1;
        isSwaped: boolean;
    } {
        const vecRootP1 = vector(point(root.coords[0], root.coords[1]), point(p1.coords[0], p1.coords[1]));
        const vecRootP2 = vector(point(root.coords[0], root.coords[1]), point(p2.coords[0], p2.coords[1]));
        const vecRootDir = vector(point(root.coords[0], root.coords[1]), point(dir.coords[0], dir.coords[1]));

        const radP1P2 = angleCCW(vecRootP1, vecRootP2);
        const radP1Dir = angleCCW(vecRootP1, vecRootDir);

        const needSwap = radP1Dir > radP1P2;

        const startLine = needSwap ? this.previewLine2 : this.previewLine1;
        const endLine = needSwap ? this.previewLine1 : this.previewLine2;

        const startLineEndIdx = getLargerIdx(startLine.startPointIdx, startLine.endPointIdx);
        const endLineEndIdx = getLargerIdx(endLine.startPointIdx, endLine.endPointIdx);

        const startLineVDir = root.relIndex === startLineEndIdx ? -1 : 1;
        const endLineVDir = root.relIndex === endLineEndIdx ? -1 : 1;

        return { startLine, endLine, startLineVDir, endLineVDir, isSwaped: needSwap };
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private doTrySelection(event: GeoPointerEvent, ctrl: GeoDocCtrl) {
        const selected = this.selLogic.trySelect(event, ctrl);
        if (selected) {
            if (selected.length === 1) {
                const pts = (selected[0] as RenderVertex[]).map(v => vert(v));

                if (pts.length >= 2) {
                    const [p1, root] = pts.slice(0, 2);
                    this.previewLine1 = pLine(ctrl, -21, RenderLineSegment, p1, root);
                    this.pQ.add(this.previewLine1);
                }

                if (pts.length === 3) {
                    const [root, p2] = pts.slice(1, 3);
                    this.previewLine2 = pLine(ctrl, -22, RenderLineSegment, root, p2);
                    this.pQ.add(this.previewLine2);
                }
            } else if (selected.length === 2) {
                const pts = (selected[0] as RenderVertex[]).map(v => vert(v));
                const dir = vert(selected[1] as RenderVertex);
                const [p1, root, p2] = pts;

                const { startLine, endLine, startLineVDir, endLineVDir } = this.calculatePreviewAngle(
                    p1,
                    root,
                    p2,
                    dir
                );

                this.previewAngle = pAngle(ctrl, -24, root, startLine, endLine, startLineVDir, endLineVDir);
                this.pQ.add(this.previewAngle);
            }
        }

        this.pQ.flush(ctrl);
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async performConstructionFromPreview(selected: SelectableType[], ctrl: GeoDocCtrl) {
        try {
            const { pcs, points } = await assignNames(
                ctrl,
                selected[0] as RenderVertex[],
                this.toolbar.getTool('NamingElementTool') as NamingElementTool,
                'Tên điểm góc',
                'Góc',
                this.previewAngle
            );

            if (!pcs) {
                this.resetState();
                return;
            }

            const [p1, root, p2] = points;
            const dir = selected[1] as RenderVertex;

            const { startLineVDir, endLineVDir, isSwaped } = this.calculatePreviewAngle(p1, root, p2, dir);

            const startLineName = isSwaped ? `${p2.name}${root.name}` : `${p1.name}${root.name}`;
            const endLineName = isSwaped ? `${root.name}${p1.name}` : `${root.name}${p2.name}`;

            const constructionAngle = buildAngleConstructionFromLines(
                startLineName,
                'LineSegment',
                endLineName,
                'LineSegment',
                this.previewAngle.name,
                root.name,
                startLineVDir,
                endLineVDir
            );

            await remoteConstruct(ctrl, constructionAngle, pcs, this.editor.geoGateway, 'góc');
        } finally {
            this.resetState();
        }
    }
}
