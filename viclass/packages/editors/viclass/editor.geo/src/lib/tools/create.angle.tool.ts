import { point, Point, vector } from '@flatten-js/core';
import { ErrorHandlerDecorator } from '@viclass/editor.core';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { CommonToolState, RenderAngle, RenderLine, RenderVertex } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { angleCCW, pAngle, PreviewQueue, pVertex } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import { nLines, strk, then, ThenSelector, vert, vertex } from '../selectors';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    buildAngleConstructionFromLines,
    buildIntersectionRequest,
    getElementConstructionDetails,
} from './util.construction';
import { createFlattenLine } from './util.flatten';
import { calculateLineLineIntersection } from './util.intersections';
import { assignNames, getFocusDocCtrl, handleIfPointerNotInError, remoteConstruct } from './util.tool';

/**
 * Unified tool to create an angle from two lines with direction point.
 * Uses advanced selector DSL for robust element selection.
 *
 * Selection Flow:
 * 1. Select two lines (stroke selector with filtering)
 * 2. Select direction point (vertex selector)
 * 3. Calculate angle with proper vector mathematics
 * 4. Preview and construct angle
 *
 * <AUTHOR>
 */
export class CreateAngleTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateAngleTool';

    declare selLogic: ThenSelector;
    private pQ = new PreviewQueue();
    private previewAngle: RenderAngle | undefined;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelectionLogic();
    }

    override resetState() {
        if (this.selLogic) this.selLogic.reset();
        this.previewAngle = undefined;
        super.resetState();
    }

    /**
     * Creates the selection logic using DSL patterns from selector module.
     * Follows the sequential selection pattern: lines -> direction point
     */
    private createSelectionLogic() {
        // Stage 1: Select two lines using nLines helper
        const lineSelector = nLines(this.pQ, this.pointerHandler.cursor, {
            count: 2,
            name: 'lineSelector',
        });

        // Stage 2: Select direction point with constraints
        const directionPointSelector = vertex({
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
            syncPreview: false,
            refinedFilter: (vertex: RenderVertex) => {
                const selectedLines = this.selLogic.selected[0] as RenderLine[];
                return !selectedLines.some((selectedStroke: any) => {
                    const line = Array.isArray(selectedStroke) ? selectedStroke[0] : selectedStroke;
                    return line.startPointIdx === vertex.relIndex || line.endPointIdx === vertex.relIndex;
                });
            },
        });

        // Main selection logic: sequential selection
        this.selLogic = then([lineSelector, directionPointSelector], {
            onComplete: (selector: ThenSelector, doc: GeoDocCtrl) =>
                this.performConstructionFromSelection(selector.selected, doc),
        });
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType === 'pointerdown' && !this.shouldHandleClick(event)) return event;

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) return event;

        if (event.eventType === 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();
        return event;
    }

    /**
     * Handles selection attempt and manages preview generation.
     * Follows the pattern from CreateAngleByThreePointsTool
     */
    private doTrySelection(event: GeoPointerEvent, ctrl: GeoDocCtrl) {
        const selected = this.selLogic.trySelect(event, ctrl);

        // Generate preview when we have both lines and direction point
        if (selected && Array.isArray(selected) && selected.length === 2) {
            const [selectedStrokes, directionPoint] = selected;
            if (Array.isArray(selectedStrokes) && selectedStrokes.length === 2) {
                const lines = selectedStrokes.map((stroke: RenderLine) => strk(stroke)) as RenderLine[];
                this.generateAnglePreview(lines, vert(directionPoint as RenderVertex), ctrl);
            }
        }

        // Flush preview queue
        this.pQ.flush(ctrl);
    }

    /**
     * Generates angle preview using simplified vector mathematics.
     * Follows the pattern from CreateAngleByThreePointsTool
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private generateAnglePreview(lines: RenderLine[], directionPoint: RenderVertex, ctrl: GeoDocCtrl) {
        try {
            // Calculate angle data using simplified approach
            const angleData = this.calculateAngleFromLines(lines, directionPoint, ctrl);
            if (!angleData) return;

            const { intersectionPoint, startDirection, endDirection, needSwap } = angleData;
            const intersectionVertex = pVertex(-25, [intersectionPoint.x, intersectionPoint.y]);

            // Create preview angle following the pattern from CreateAngleByThreePointsTool
            this.previewAngle = pAngle(
                ctrl,
                -24, // Preview ID following convention
                intersectionVertex,
                needSwap ? lines[1] : lines[0],
                needSwap ? lines[0] : lines[1],
                startDirection,
                endDirection
            );

            this.pQ.add(this.previewAngle);
        } catch (error) {
            console.warn('Error generating angle preview:', error);
        }
    }

    /**
     * Simplified calculation for angle from two lines.
     * Uses approach similar to CreateAngleByThreePointsTool but adapted for lines.
     */
    private calculateAngleFromLines(
        lines: RenderLine[],
        directionPoint: RenderVertex,
        ctrl: GeoDocCtrl
    ):
        | {
              intersectionPoint: Point;
              startDirection: 1 | -1;
              endDirection: 1 | -1;
              needSwap: boolean;
          }
        | undefined {
        // Convert render lines to flatten-js lines
        const fLines = lines.map(renderLine => createFlattenLine(renderLine, ctrl));

        if (fLines.length !== 2) return undefined;

        // Calculate intersection point
        const intersection = calculateLineLineIntersection(lines[0], lines[1], ctrl);
        if (!intersection || intersection.length === 0) return undefined;

        const intersectionPoint = intersection[0] as Point;
        const directionPt = point(directionPoint.coords[0], directionPoint.coords[1]);

        // Get line direction vectors (tangent vectors)
        const line1OrderedVector = lines[0].orderedVector(ctrl.rendererCtrl);
        const line2OrderedVector = lines[1].orderedVector(ctrl.rendererCtrl);

        const line1Dir = vector(line1OrderedVector[0], line1OrderedVector[1]);
        const line2Dir = vector(line2OrderedVector[0], line2OrderedVector[1]);

        // Calculate direction vector from intersection to direction point
        const directionVector = vector(intersectionPoint, directionPt);

        // Try all 4 combinations of line directions
        const combinations = [
            { line1: line1Dir, line2: line2Dir, dir1: 1, dir2: 1 },
            { line1: line1Dir, line2: line2Dir.multiply(-1), dir1: 1, dir2: -1 },
            { line1: line1Dir.multiply(-1), line2: line2Dir, dir1: -1, dir2: 1 },
            { line1: line1Dir.multiply(-1), line2: line2Dir.multiply(-1), dir1: -1, dir2: -1 },
        ];

        for (const combo of combinations) {
            const needSwap = angleCCW(combo.line1, combo.line2) > Math.PI;

            const startVector = needSwap ? combo.line2 : combo.line1;
            const endVector = needSwap ? combo.line1 : combo.line2;

            const angleBetweenLines = angleCCW(startVector, endVector);
            const angleToDirection = angleCCW(startVector, directionVector);

            if (angleToDirection <= angleBetweenLines) {
                const startDirection = needSwap ? combo.dir2 : combo.dir1;
                const endDirection = needSwap ? combo.dir1 : combo.dir2;

                return {
                    intersectionPoint,
                    startDirection: startDirection as 1 | -1,
                    endDirection: endDirection as 1 | -1,
                    needSwap,
                };
            }
        }

        return undefined;
    }

    /**
     * Main construction method with comprehensive error handling.
     * Follows the pattern from CreateAngleByThreePointsTool
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async performConstructionFromSelection(selected: any[], ctrl: GeoDocCtrl) {
        try {
            const [selectedStrokes, directionPoint] = selected;

            if (!Array.isArray(selectedStrokes) || selectedStrokes.length !== 2) {
                throw new Error('Invalid selection for angle construction');
            }

            // Extract lines from SelectedStroke format
            const lines = selectedStrokes.map(stroke => (Array.isArray(stroke) ? stroke[0] : stroke)) as RenderLine[];
            await this.performAngleConstruction(lines, directionPoint, ctrl);
        } catch (error) {
            console.error('Error in angle construction:', error);
            this.resetState();
            throw error;
        }
    }

    /**
     * Performs the actual angle construction with proper naming and awareness.
     * Implements the construction pattern from the documentation.
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async performAngleConstruction(lines: RenderLine[], directionPoint: RenderVertex, ctrl: GeoDocCtrl) {
        // Calculate angle data
        const angleData = this.calculateAngleFromLines(lines, directionPoint, ctrl);
        if (!angleData) {
            throw new Error('Cannot calculate angle from selected lines');
        }

        const { intersectionPoint, startDirection, endDirection, needSwap } = angleData;

        // Request angle name from user
        const intersectionVertex = pVertex(-25, [intersectionPoint.x, intersectionPoint.y]);

        const { pcs, points } = await assignNames(
            ctrl,
            [intersectionVertex],
            this.toolbar.getTool('NamingElementTool') as NamingElementTool,
            'Tên điểm góc',
            'Góc',
            this.previewAngle
        );

        if (!pcs) {
            this.resetState();
            return;
        }

        const intersectionConstruction = buildIntersectionRequest({
            cgName: 'LineLine',
            outputName: points[0].name,
            paramA: getElementConstructionDetails(lines[0]),
            paramB: getElementConstructionDetails(lines[1]),
        });

        // Build angle construction request
        const angleConstruction = buildAngleConstructionFromLines(
            lines[needSwap ? 1 : 0].name,
            lines[needSwap ? 1 : 0].elType,
            lines[needSwap ? 0 : 1].name,
            lines[needSwap ? 0 : 1].elType,
            this.previewAngle.name,
            points[0].name,
            startDirection,
            endDirection
        );

        try {
            await remoteConstruct(ctrl, angleConstruction, [intersectionConstruction], this.editor.geoGateway, 'góc');
        } finally {
            this.resetState();
        }
    }
}
